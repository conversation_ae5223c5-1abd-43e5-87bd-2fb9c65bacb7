import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signage/utils/platform_utils.dart';

// Platform-specific imports
import 'package:flutter_libserialport/flutter_libserialport.dart' if (dart.library.html) 'dart:html';
import 'package:usb_serial/usb_serial.dart' if (dart.library.html) 'dart:html';

/// Detection state enum for LD2410B sensor
enum DetectionState { none, moving, static, combined }

/// Protocol-compliant data structure for LD2410B sensor readings
class LD2410Data {
  final int movingTargetDistance;
  final int movingTargetEnergy;
  final int staticTargetDistance;
  final int staticTargetEnergy;
  final int detectionDistance;
  final DetectionState detectionState;

  LD2410Data({
    required this.movingTargetDistance,
    required this.movingTargetEnergy,
    required this.staticTargetDistance,
    required this.staticTargetEnergy,
    required this.detectionDistance,
    required this.detectionState,
  });

  @override
  String toString() {
    return 'Detection: ${detectionState.name} | '
           'Distance: ${detectionDistance}cm | '
           'Moving: ${movingTargetDistance}cm ($movingTargetEnergy%) | '
           'Static: ${staticTargetDistance}cm ($staticTargetEnergy%)';
  }
}

/// Cross-platform serial communication service for LD2410B sensor
class SerialCommunicationService {
  static final SerialCommunicationService _instance = SerialCommunicationService._internal();
  factory SerialCommunicationService() => _instance;
  SerialCommunicationService._internal();

  // Platform-specific controllers
  SerialPort? _desktopPort;
  UsbPort? _androidPort;
  
  // Connection state
  bool _isConnected = false;
  bool _isInitialized = false;
  
  // Data stream
  StreamController<LD2410Data>? _dataController;
  Stream<LD2410Data>? _dataStream;

  // Buffer for incoming data
  final List<int> _buffer = [];

  // Protocol header and footer for LD2410B
  static const _header = [0xF4, 0xF3, 0xF2, 0xF1];
  static const _footer = [0xF8, 0xF7, 0xF6, 0xF5];

  /// Get the data stream
  Stream<LD2410Data>? get dataStream => _dataStream;

  /// Get the distance stream (for backward compatibility)
  Stream<int?>? get distanceStream => _dataStream?.map((data) => data.detectionDistance);

  /// Check if the service is connected
  bool get isConnected => _isConnected;

  /// Initialize the serial communication service
  Future<bool> initialize() async {
    if (_isInitialized) {
      debugPrint('SerialCommunicationService: Already initialized');
      return _isConnected;
    }

    debugPrint('SerialCommunicationService: Initializing for platform: ${PlatformUtils.platformName}');

    try {
      _dataController = StreamController<LD2410Data>.broadcast();
      _dataStream = _dataController!.stream;

      if (PlatformUtils.isAndroid) {
        await _initializeAndroid();
      } else if (PlatformUtils.isDesktop) {
        await _initializeDesktop();
      } else {
        debugPrint('SerialCommunicationService: Unsupported platform');
        _isInitialized = true; // Mark as initialized to prevent retries
        return false;
      }

      _isInitialized = true;
      debugPrint('SerialCommunicationService: Initialization completed. Connected: $_isConnected');
      return _isConnected;
    } catch (e) {
      debugPrint('SerialCommunicationService: Initialization failed: $e');
      _isInitialized = true; // Mark as initialized to prevent retries
      return false;
    }
  }

  /// Initialize Android USB serial communication
  Future<void> _initializeAndroid() async {
    try {
      debugPrint('SerialCommunicationService: Initializing Android USB serial');

      // Get list of available USB devices
      List<UsbDevice> devices = await UsbSerial.listDevices();
      debugPrint('SerialCommunicationService: Found ${devices.length} USB devices');

      // Look for LD2410B device with correct VID/PID
      UsbDevice? targetDevice;
      for (UsbDevice device in devices) {
        debugPrint('SerialCommunicationService: Device - VID: ${device.vid}, PID: ${device.pid}');
        // LD2410B specific VID/PID (CH340 chip)
        // Vendor ID: 6790 (0x1a86), Product ID: 29987 (0x7523)
        if (device.vid == 6790 && device.pid == 29987) {
          debugPrint('SerialCommunicationService: Found LD2410B device (VID: 6790, PID: 29987)');
          targetDevice = device;
          break;
        }
      }

      if (targetDevice == null) {
        debugPrint('SerialCommunicationService: LD2410B device not found');
        return;
      }

      debugPrint('SerialCommunicationService: Found LD2410B device');

      // Create USB port using the correct API
      _androidPort = await targetDevice.create();

      if (_androidPort == null) {
        debugPrint('SerialCommunicationService: Failed to create USB port');
        return;
      }

      // Open the port
      bool openResult = await _androidPort!.open();
      if (!openResult) {
        debugPrint('SerialCommunicationService: Failed to open USB port');
        return;
      }

      // Configure port settings
      await _androidPort!.setDTR(true);
      await _androidPort!.setRTS(true);
      await _androidPort!.setPortParameters(115200, UsbPort.DATABITS_8, UsbPort.STOPBITS_1, UsbPort.PARITY_NONE);

      _isConnected = true;
      debugPrint('SerialCommunicationService: Android USB port opened successfully');

      // Start reading data
      _startAndroidDataReading();

    } catch (e) {
      debugPrint('SerialCommunicationService: Android initialization error: $e');
    }
  }

  /// Initialize desktop serial communication
  Future<void> _initializeDesktop() async {
    try {
      debugPrint('SerialCommunicationService: Initializing desktop serial');

      // Add timeout to prevent hanging
      await Future.any([
        _initializeDesktopWithTimeout(),
        Future.delayed(const Duration(seconds: 10), () {
          debugPrint('SerialCommunicationService: Desktop initialization timeout after 10 seconds');
        }),
      ]);

    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop initialization error: $e');
    }
  }

  /// Initialize desktop serial communication with timeout protection
  Future<void> _initializeDesktopWithTimeout() async {
    try {
      debugPrint('SerialCommunicationService: Starting desktop serial initialization');

      // Add delay before attempting to access ports (recommended for LD2410B stability on Linux)
      await Future.delayed(const Duration(milliseconds: 1000));

      // Get list of available serial ports
      final availablePorts = SerialPort.availablePorts;
      debugPrint('SerialCommunicationService: Available ports: $availablePorts');

      if (availablePorts.isEmpty) {
        debugPrint('SerialCommunicationService: No serial ports available');
        return;
      }

      // Try to find LD2410B device by testing each port
      for (String portName in availablePorts) {
        try {
          debugPrint('SerialCommunicationService: Trying port: $portName');

          // Check if port is available using the recommended method
          if (!SerialPort.availablePorts.contains(portName)) {
            debugPrint('SerialCommunicationService: Port $portName not available');
            continue;
          }

          _desktopPort = SerialPort(portName);

          // Get port info for debugging and check if it's LD2410B
          _logPortInfo(portName);

          // Check if this port matches LD2410B VID/PID
          if (!_isLD2410BDevice(_desktopPort!)) {
            debugPrint('SerialCommunicationService: Port $portName is not LD2410B device, skipping');
            _desktopPort!.dispose();
            _desktopPort = null;
            continue;
          }

          debugPrint('SerialCommunicationService: Port $portName appears to be LD2410B device');

          // Add delay before opening port (recommended for LD2410B stability)
          await Future.delayed(const Duration(milliseconds: 300));

          // Try to open the port using the recommended approach
          debugPrint('SerialCommunicationService: Attempting to open port: $portName');

          if (!_desktopPort!.openReadWrite()) {
            final lastError = SerialPort.lastError;
            debugPrint('SerialCommunicationService: Failed to open port: $portName, error: $lastError');
            _desktopPort!.dispose();
            _desktopPort = null;
            continue;
          }

          debugPrint('SerialCommunicationService: Successfully opened port: $portName');

          // Configure port using the recommended approach (get existing config and modify)
          final config = _desktopPort!.config;
          config.baudRate = 115200; // LD2410B working baud rate
          config.bits = 8;
          config.stopBits = 1;
          config.parity = SerialPortParity.none;
          config.setFlowControl(SerialPortFlowControl.none);

          // Apply the configuration
          _desktopPort!.config = config;

          debugPrint('SerialCommunicationService: Port configuration set successfully');
          _isConnected = true;

          // Add delay after configuration before starting data reading (recommended for LD2410B)
          await Future.delayed(const Duration(milliseconds: 500));

          // Start reading data
          _startDesktopDataReading();
          break;
        } catch (e) {
          debugPrint('SerialCommunicationService: Error with port $portName: $e');
          _desktopPort?.dispose();
          _desktopPort = null;
        }
      }

      if (!_isConnected) {
        debugPrint('SerialCommunicationService: Could not connect to any serial port');
      }

    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop initialization with timeout error: $e');
    }
  }

  /// Start reading data from Android USB port
  void _startAndroidDataReading() {
    if (_androidPort == null) return;

    debugPrint('SerialCommunicationService: Starting Android data reading');

    // Listen to the input stream from the USB port
    _androidPort!.inputStream!.listen(
      (data) {
        if (data.isNotEmpty) {
          _processData(Uint8List.fromList(data));
        }
      },
      onError: (error) {
        debugPrint('SerialCommunicationService: Android read error: $error');
      },
    );
  }

  /// Start reading data from desktop serial port
  void _startDesktopDataReading() {
    if (_desktopPort == null) return;

    debugPrint('SerialCommunicationService: Starting desktop data reading');

    try {
      // Use SerialPortReader as recommended in the sample code
      final reader = SerialPortReader(_desktopPort!);
      reader.stream.listen(
        (data) {
          // Always log that we received data for debugging
          _processData(Uint8List.fromList(data));
        },
        onError: (error) {
          debugPrint('SerialCommunicationService: Desktop read error: $error');
        },
        onDone: () {
          debugPrint('SerialCommunicationService: Desktop read stream closed');
        },
      );

      debugPrint('SerialCommunicationService: Desktop data reading started successfully');
    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop read setup error: $e');
    }
  }

  /// Process incoming data using protocol-compliant LD2410B frame detection
  void _processData(Uint8List data) {
    debugPrint('SerialCommunicationService: Received ${data.length} bytes: $data');
    _buffer.addAll(data);

    // Look for frame header in buffer
    while (_buffer.length >= 4) {
      // Find the start of a frame
      int frameStart = -1;
      for (int i = 0; i <= _buffer.length - 4; i++) {
        if (_buffer[i] == _header[0] &&
            _buffer[i + 1] == _header[1] &&
            _buffer[i + 2] == _header[2] &&
            _buffer[i + 3] == _header[3]) {
          frameStart = i;
          break;
        }
      }

      if (frameStart == -1) {
        // No frame header found, clear buffer
        debugPrint('SerialCommunicationService: No frame header found in buffer of ${_buffer.length} bytes');
        _buffer.clear();
        break;
      }

      // Remove any bytes before the frame start
      if (frameStart > 0) {
        _buffer.removeRange(0, frameStart);
        debugPrint('SerialCommunicationService: Removed $frameStart bytes before frame header');
      }

      // Check if we have enough data for minimum frame (header + length + command = 7 bytes)
      if (_buffer.length < 7) {
        debugPrint('SerialCommunicationService: Waiting for more data, current buffer size: ${_buffer.length}');
        break;
      }

      // Get data length from frame (includes command byte)
      final dataLength = _buffer[4] | (_buffer[5] << 8);
      final totalFrameLength = 4 + 2 + dataLength + 4; // header + length + (command + data) + footer

      debugPrint('SerialCommunicationService: Data length: $dataLength, total frame length: $totalFrameLength');

      // Check if we have complete frame
      if (_buffer.length < totalFrameLength) {
        debugPrint('SerialCommunicationService: Waiting for complete frame, have ${_buffer.length}, need $totalFrameLength');
        break;
      }

      // Extract complete frame
      final frame = _buffer.sublist(0, totalFrameLength);
      debugPrint('SerialCommunicationService: Attempting to validate frame: $frame');

      if (_validateFrame(frame)) {
        final parsed = _parseDataFrame(frame);
        if (parsed != null) {
          _dataController?.add(parsed);
          debugPrint('SerialCommunicationService: LD2410Data extracted: $parsed');
        }
        _buffer.removeRange(0, totalFrameLength);
      } else {
        debugPrint('SerialCommunicationService: Frame validation failed, removing first byte');
        _buffer.removeAt(0);
      }
    }
  }

  /// Validate frame header and footer according to LD2410B protocol
  bool _validateFrame(List<int> frame) {
    debugPrint('SerialCommunicationService: Validating frame of length ${frame.length}');

    // Check header
    if (frame.length < 4 ||
        frame[0] != _header[0] ||
        frame[1] != _header[1] ||
        frame[2] != _header[2] ||
        frame[3] != _header[3]) {
      debugPrint('SerialCommunicationService: Header validation failed');
      return false;
    }

    // Check footer
    final footerStart = frame.length - 4;
    if (frame.length < 8 ||
        frame[footerStart] != _footer[0] ||
        frame[footerStart + 1] != _footer[1] ||
        frame[footerStart + 2] != _footer[2] ||
        frame[footerStart + 3] != _footer[3]) {
      debugPrint('SerialCommunicationService: Footer validation failed');
      return false;
    }

    debugPrint('SerialCommunicationService: Frame validation successful');
    return true;
  }

  /// Parse LD2410B data frame according to protocol specification
  LD2410Data? _parseDataFrame(List<int> frame) {
    try {
      // Frame structure: [header(4)] [length(2)] [command(1)] [data(variable)] [footer(4)]
      // Data starts at index 7
      final dataStart = 7;

      debugPrint('SerialCommunicationService: Parsing frame data starting at index $dataStart');
      debugPrint('SerialCommunicationService: Data bytes: ${frame.sublist(dataStart, frame.length - 4)}');

      // Based on the actual data structure observed:
      // Data: [170, 2, 110, 0, 0, 110, 0, 100, 94, 0, 85, 0]
      // This appears to be: [target_state, moving_distance_low, moving_distance_high, moving_energy, static_distance_low, static_distance_high, static_energy, detection_distance_low, detection_distance_high, ?, ?, ?]

      final targetState = frame[dataStart + 1]; // Index 8: target state (2 = moving, 3 = both)
      final movingDistance = frame[dataStart + 2] | (frame[dataStart + 3] << 8); // Index 9-10
      final movingEnergy = frame[dataStart + 4]; // Index 11
      final staticDistance = frame[dataStart + 5] | (frame[dataStart + 6] << 8); // Index 12-13
      final staticEnergy = frame[dataStart + 7]; // Index 14
      final detectionDistance = frame[dataStart + 8] | (frame[dataStart + 9] << 8); // Index 15-16

      // Map target state to detection state
      DetectionState detectionState;
      switch (targetState) {
        case 0:
          detectionState = DetectionState.none;
          break;
        case 1:
          detectionState = DetectionState.moving;
          break;
        case 2:
          detectionState = DetectionState.static;
          break;
        case 3:
          detectionState = DetectionState.combined;
          break;
        default:
          detectionState = DetectionState.none;
      }

      final result = LD2410Data(
        movingTargetDistance: movingDistance,
        movingTargetEnergy: movingEnergy,
        staticTargetDistance: staticDistance,
        staticTargetEnergy: staticEnergy,
        detectionDistance: detectionDistance,
        detectionState: detectionState,
      );

      debugPrint('SerialCommunicationService: Parsed data - State: $targetState, Moving: ${movingDistance}cm/$movingEnergy%, Static: ${staticDistance}cm/$staticEnergy%, Detection: ${detectionDistance}cm');

      return result;
    } catch (e) {
      debugPrint('SerialCommunicationService: Parsing error: $e');
      return null;
    }
  }



  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    debugPrint('SerialCommunicationService: Disposing');

    if (PlatformUtils.isAndroid && _androidPort != null) {
      await _androidPort!.close();
      _androidPort = null;
    }

    if (PlatformUtils.isDesktop && _desktopPort != null) {
      _desktopPort!.close();
      _desktopPort!.dispose();
      _desktopPort = null;
    }

    await _dataController?.close();
    _dataController = null;
    _dataStream = null;

    _isConnected = false;
    _isInitialized = false;
    _buffer.clear();
  }



  /// Log port information for debugging
  void _logPortInfo(String portName) {
    try {
      debugPrint('SerialCommunicationService: Port info for $portName:');
      debugPrint('  - Vendor ID: ${_desktopPort?.vendorId ?? 'N/A'}');
      debugPrint('  - Product ID: ${_desktopPort?.productId ?? 'N/A'}');
      debugPrint('  - Description: ${_desktopPort?.description ?? 'N/A'}');
    } catch (e) {
      debugPrint('SerialCommunicationService: Error getting port info for $portName: $e');
    }
  }



  /// Check if the serial port is an LD2410B device
  bool _isLD2410BDevice(SerialPort port) {
    try {
      // Check vendor ID and product ID
      final vendorId = port.vendorId;
      final productId = port.productId;

      debugPrint('SerialCommunicationService: Port VID: $vendorId, PID: $productId');

      // LD2410B uses CH340 chip: VID=6790 (0x1a86), PID=29987 (0x7523)
      if (vendorId == 6790 && productId == 29987) {
        debugPrint('SerialCommunicationService: Confirmed LD2410B device (VID: 6790, PID: 29987)');
        return true;
      }

      // Also check hex values in case the library returns hex
      if (vendorId == 0x1a86 && productId == 0x7523) {
        debugPrint('SerialCommunicationService: Confirmed LD2410B device (VID: 0x1a86, PID: 0x7523)');
        return true;
      }

      debugPrint('SerialCommunicationService: Not LD2410B device (VID: $vendorId, PID: $productId)');
      return false;
    } catch (e) {
      debugPrint('SerialCommunicationService: Error checking device VID/PID: $e');
      // If we can't determine the device type, assume it might be LD2410B
      return true;
    }
  }
}
